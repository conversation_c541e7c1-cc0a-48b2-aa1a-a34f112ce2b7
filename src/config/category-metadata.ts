import { CertificateCategory, TemplateCategory } from '@/types/certificate';

/**
 * 证书分类元数据配置
 * 基于Google SEO最佳实践和高搜索量关键词优化
 */

export const CATEGORY_METADATA: Record<CertificateCategory, TemplateCategory> = {
  // 高搜索量分类
  [CertificateCategory.GIFT_CERTIFICATE]: {
    id: CertificateCategory.GIFT_CERTIFICATE,
    name: 'gift-certificate',
    displayName: 'Gift Certificates',
    description: 'Create beautiful gift certificates for businesses, restaurants, spas, and special occasions. Professional templates with customizable designs.',
    seoKeywords: [
      'gift certificate template',
      'gift certificate maker',
      'business gift certificate',
      'restaurant gift certificate',
      'spa gift certificate',
      'printable gift certificate',
      'custom gift certificate',
      'gift voucher template'
    ],
    urlSlug: 'gift-certificates',
    metaTitle: 'Free Gift Certificate Templates | Professional Gift Certificate Maker',
    metaDescription: 'Create stunning gift certificates with our free templates. Perfect for businesses, restaurants, spas, and special occasions. Download as PDF instantly.',
    templates: [],
    defaultSize: 'landscape',
    defaultTemplate: 'gift-certificate-1',
    enabled: false
  },

  [CertificateCategory.AWARD_CERTIFICATE]: {
    id: CertificateCategory.AWARD_CERTIFICATE,
    name: 'award-certificate',
    displayName: 'Award Certificates',
    description: 'Professional award certificate templates for recognizing outstanding performance, achievements, and excellence in various fields.',
    seoKeywords: [
      'award certificate template',
      'award certificate maker',
      'employee award certificate',
      'student award certificate',
      'performance award certificate',
      'recognition award template',
      'achievement award certificate',
      'excellence award template'
    ],
    urlSlug: 'award-certificates',
    metaTitle: 'Free Award Certificate Templates | Professional Award Certificate Maker',
    metaDescription: 'Create professional award certificates for employees, students, and achievements. Beautiful templates with instant PDF download.',
    templates: [],
    defaultSize: 'landscape',
    defaultTemplate: 'award-certificate-1',
    enabled: false
  },

  [CertificateCategory.GRADUATION_CERTIFICATE]: {
    id: CertificateCategory.GRADUATION_CERTIFICATE,
    name: 'graduation-certificate',
    displayName: 'Graduation Certificates',
    description: 'Elegant graduation certificate templates for schools, universities, and educational programs. Celebrate academic achievements with style.',
    seoKeywords: [
      'graduation certificate template',
      'graduation certificate maker',
      'school graduation certificate',
      'university graduation certificate',
      'diploma certificate template',
      'academic achievement certificate',
      'graduation diploma template',
      'educational certificate'
    ],
    urlSlug: 'graduation-certificates',
    metaTitle: 'Free Graduation Certificate Templates | Academic Achievement Certificates',
    metaDescription: 'Create beautiful graduation certificates for schools and universities. Professional academic templates with instant PDF download.',
    templates: [],
    defaultSize: 'portrait',
    defaultTemplate: 'graduation-certificate-1',
    enabled: false
  },

  [CertificateCategory.SPORTS_CERTIFICATE]: {
    id: CertificateCategory.SPORTS_CERTIFICATE,
    name: 'sports-certificate',
    displayName: 'Sports Certificates',
    description: 'Dynamic sports certificate templates for athletes, teams, and sporting events. Perfect for tournaments, leagues, and athletic achievements.',
    seoKeywords: [
      'sports certificate template',
      'sports award certificate',
      'athletic certificate template',
      'sports achievement certificate',
      'team sports certificate',
      'tournament certificate',
      'sports participation certificate',
      'athletic award template'
    ],
    urlSlug: 'sports-certificates',
    metaTitle: 'Free Sports Certificate Templates | Athletic Award Certificates',
    metaDescription: 'Create dynamic sports certificates for athletes and teams. Professional athletic templates for tournaments and achievements.',
    templates: [],
    defaultSize: 'landscape',
    defaultTemplate: 'sports-certificate-1',
    enabled: false
  },

  [CertificateCategory.BASKETBALL_CERTIFICATE]: {
    id: CertificateCategory.BASKETBALL_CERTIFICATE,
    name: 'basketball-certificate',
    displayName: 'Basketball Certificates',
    description: 'Specialized basketball certificate templates for players, coaches, and teams. Perfect for MVP awards, season recognition, and tournament victories.',
    seoKeywords: [
      'basketball certificate template',
      'basketball award certificate',
      'basketball MVP certificate',
      'basketball team certificate',
      'basketball player certificate',
      'basketball tournament certificate',
      'basketball achievement award',
      'basketball recognition certificate'
    ],
    urlSlug: 'basketball-certificates',
    metaTitle: 'Free Basketball Certificate Templates | Basketball Award Certificates',
    metaDescription: 'Create professional basketball certificates for players, coaches, and teams. MVP awards, tournament victories, and season recognition templates.',
    templates: [],
    defaultSize: 'landscape',
    defaultTemplate: 'basketball-certificate-1',
    enabled: false
  },

  [CertificateCategory.FOOTBALL_CERTIFICATE]: {
    id: CertificateCategory.FOOTBALL_CERTIFICATE,
    name: 'football-certificate',
    displayName: 'Football Certificates',
    description: 'Professional football certificate templates for players, coaches, and teams. Ideal for MVP awards, championship victories, and season achievements.',
    seoKeywords: [
      'football certificate template',
      'football award certificate',
      'football MVP certificate',
      'football team certificate',
      'football player certificate',
      'football championship certificate',
      'football achievement award',
      'football recognition certificate'
    ],
    urlSlug: 'football-certificates',
    metaTitle: 'Free Football Certificate Templates | Football Award Certificates',
    metaDescription: 'Create professional football certificates for players, coaches, and teams. Championship victories, MVP awards, and season achievement templates.',
    templates: [],
    defaultSize: 'landscape',
    defaultTemplate: 'football-certificate-1',
    enabled: false
  },

  [CertificateCategory.TRAINING_CERTIFICATE]: {
    id: CertificateCategory.TRAINING_CERTIFICATE,
    name: 'training-certificate',
    displayName: 'Training Certificates',
    description: 'Professional training certificate templates for corporate training, workshops, and skill development programs.',
    seoKeywords: [
      'training certificate template',
      'training completion certificate',
      'corporate training certificate',
      'professional development certificate',
      'skill training certificate',
      'workshop certificate template',
      'certification training template',
      'employee training certificate'
    ],
    urlSlug: 'training-certificates',
    metaTitle: 'Free Training Certificate Templates | Professional Development Certificates',
    metaDescription: 'Create professional training certificates for corporate programs and workshops. Skill development and completion certificate templates.',
    templates: [],
    defaultSize: 'portrait',
    defaultTemplate: 'training-certificate-1',
    enabled: false
  },

  [CertificateCategory.EMPLOYEE_CERTIFICATE]: {
    id: CertificateCategory.EMPLOYEE_CERTIFICATE,
    name: 'employee-certificate',
    displayName: 'Employee Certificates',
    description: 'Corporate employee certificate templates for recognition, appreciation, and workplace achievements.',
    seoKeywords: [
      'employee certificate template',
      'employee recognition certificate',
      'employee appreciation certificate',
      'employee of the month certificate',
      'workplace achievement certificate',
      'corporate recognition certificate',
      'staff appreciation certificate',
      'employee award template'
    ],
    urlSlug: 'employee-certificates',
    metaTitle: 'Free Employee Certificate Templates | Corporate Recognition Certificates',
    metaDescription: 'Create professional employee certificates for workplace recognition and appreciation. Corporate templates for staff achievements.',
    templates: [],
    defaultSize: 'landscape',
    defaultTemplate: 'employee-certificate-1',
    enabled: false
  },

  [CertificateCategory.VOLUNTEER_CERTIFICATE]: {
    id: CertificateCategory.VOLUNTEER_CERTIFICATE,
    name: 'volunteer-certificate',
    displayName: 'Volunteer Certificates',
    description: 'Appreciation certificate templates for volunteers and community service recognition.',
    seoKeywords: [
      'volunteer certificate template',
      'volunteer appreciation certificate',
      'community service certificate',
      'volunteer recognition certificate',
      'volunteer award template',
      'community volunteer certificate',
      'volunteer service certificate',
      'volunteer appreciation award'
    ],
    urlSlug: 'volunteer-certificates',
    metaTitle: 'Free Volunteer Certificate Templates | Community Service Recognition',
    metaDescription: 'Create meaningful volunteer certificates for community service recognition. Appreciation templates for volunteer contributions.',
    templates: [],
    defaultSize: 'portrait',
    defaultTemplate: 'volunteer-certificate-1',
    enabled: false
  },

  [CertificateCategory.RECOGNITION_CERTIFICATE]: {
    id: CertificateCategory.RECOGNITION_CERTIFICATE,
    name: 'recognition-certificate',
    displayName: 'Recognition Certificates',
    description: 'General recognition certificate templates for various achievements and contributions.',
    seoKeywords: [
      'recognition certificate template',
      'recognition award certificate',
      'achievement recognition certificate',
      'contribution recognition certificate',
      'service recognition certificate',
      'performance recognition certificate',
      'general recognition template',
      'appreciation recognition certificate'
    ],
    urlSlug: 'recognition-certificates',
    metaTitle: 'Free Recognition Certificate Templates | Achievement Recognition Awards',
    metaDescription: 'Create professional recognition certificates for various achievements and contributions. General purpose recognition templates.',
    templates: [],
    defaultSize: 'landscape',
    defaultTemplate: 'recognition-certificate-1',
    enabled: false
  },

  [CertificateCategory.FUNNY_CERTIFICATE]: {
    id: CertificateCategory.FUNNY_CERTIFICATE,
    name: 'funny-certificate',
    displayName: 'Funny Certificates',
    description: 'Humorous and entertaining certificate templates for fun awards, gag gifts, and lighthearted recognition.',
    seoKeywords: [
      'funny certificate template',
      'humorous certificate template',
      'gag certificate template',
      'joke certificate template',
      'entertaining certificate template',
      'fun award certificate',
      'comedy certificate template',
      'silly certificate template'
    ],
    urlSlug: 'funny-certificates',
    metaTitle: 'Free Funny Certificate Templates | Humorous Award Certificates',
    metaDescription: 'Create hilarious funny certificates for gag gifts and entertaining awards. Humorous templates for lighthearted recognition.',
    templates: [],
    defaultSize: 'landscape',
    defaultTemplate: 'funny-certificate-1',
    enabled: false
  },

  [CertificateCategory.BIRTH_CERTIFICATE]: {
    id: CertificateCategory.BIRTH_CERTIFICATE,
    name: 'birth-certificate',
    displayName: 'Birth Certificates',
    description: 'Commemorative birth certificate templates for newborns and keepsake purposes. Note: These are for commemorative use only, not official documents.',
    seoKeywords: [
      'birth certificate template',
      'commemorative birth certificate',
      'newborn certificate template',
      'baby birth certificate',
      'birth announcement certificate',
      'keepsake birth certificate',
      'decorative birth certificate',
      'birth record template'
    ],
    urlSlug: 'birth-certificates',
    metaTitle: 'Free Birth Certificate Templates | Commemorative Newborn Certificates',
    metaDescription: 'Create beautiful commemorative birth certificates for newborns. Keepsake templates for birth announcements and memories.',
    templates: [],
    defaultSize: 'portrait',
    defaultTemplate: 'birth-certificate-1',
    enabled: false
  },

  // 原有分类
  [CertificateCategory.ACHIEVEMENT]: {
    id: CertificateCategory.ACHIEVEMENT,
    name: 'achievement',
    displayName: 'Achievement Certificates',
    description: 'Professional achievement certificate templates for recognizing accomplishments and milestones.',
    seoKeywords: [
      'achievement certificate template',
      'achievement award certificate',
      'accomplishment certificate',
      'milestone certificate',
      'success certificate template',
      'achievement recognition certificate'
    ],
    urlSlug: 'achievement-certificates',
    metaTitle: 'Free Achievement Certificate Templates | Professional Achievement Awards',
    metaDescription: 'Create professional achievement certificates for recognizing accomplishments and milestones. Beautiful templates with instant PDF download.',
    templates: [],
    defaultSize: 'landscape',
    defaultTemplate: 'achievement-template-1',
    enabled: true
  },

  [CertificateCategory.COMPLETION]: {
    id: CertificateCategory.COMPLETION,
    name: 'completion',
    displayName: 'Completion Certificates',
    description: 'Professional completion certificate templates for courses, training programs, and educational achievements.',
    seoKeywords: [
      'completion certificate template',
      'course completion certificate',
      'training completion certificate',
      'program completion certificate',
      'educational completion certificate',
      'certificate of completion template'
    ],
    urlSlug: 'completion-certificates',
    metaTitle: 'Free Completion Certificate Templates | Course & Training Certificates',
    metaDescription: 'Create professional completion certificates for courses and training programs. Educational achievement templates with instant PDF download.',
    templates: [],
    defaultSize: 'portrait',
    defaultTemplate: 'completion-template-1',
    enabled: true
  },

  [CertificateCategory.PARTICIPATION]: {
    id: CertificateCategory.PARTICIPATION,
    name: 'participation',
    displayName: 'Participation Certificates',
    description: 'Professional participation certificate templates for events, workshops, and activities.',
    seoKeywords: [
      'participation certificate template',
      'event participation certificate',
      'workshop participation certificate',
      'activity participation certificate',
      'conference participation certificate',
      'seminar participation certificate'
    ],
    urlSlug: 'participation-certificates',
    metaTitle: 'Free Participation Certificate Templates | Event & Workshop Certificates',
    metaDescription: 'Create professional participation certificates for events and workshops. Activity participation templates with instant PDF download.',
    templates: [],
    defaultSize: 'portrait',
    defaultTemplate: 'participation-template-1',
    enabled: false
  },

  [CertificateCategory.EXCELLENCE]: {
    id: CertificateCategory.EXCELLENCE,
    name: 'excellence',
    displayName: 'Excellence Certificates',
    description: 'Premium excellence certificate templates for outstanding performance and exceptional achievements.',
    seoKeywords: [
      'excellence certificate template',
      'excellence award certificate',
      'outstanding performance certificate',
      'exceptional achievement certificate',
      'premium excellence certificate',
      'distinguished excellence certificate'
    ],
    urlSlug: 'excellence-certificates',
    metaTitle: 'Free Excellence Certificate Templates | Outstanding Performance Awards',
    metaDescription: 'Create premium excellence certificates for outstanding performance. Exceptional achievement templates with instant PDF download.',
    templates: [],
    defaultSize: 'landscape',
    defaultTemplate: 'excellence-template-1',
    enabled: false
  },

  [CertificateCategory.CUSTOM]: {
    id: CertificateCategory.CUSTOM,
    name: 'custom',
    displayName: 'Custom Certificates',
    description: 'Customizable certificate templates for any purpose or occasion.',
    seoKeywords: [
      'custom certificate template',
      'customizable certificate',
      'personalized certificate template',
      'blank certificate template',
      'generic certificate template'
    ],
    urlSlug: 'custom-certificates',
    metaTitle: 'Free Custom Certificate Templates | Customizable Certificate Maker',
    metaDescription: 'Create custom certificates for any purpose. Fully customizable templates with instant PDF download.',
    templates: [],
    defaultSize: 'landscape',
    defaultTemplate: 'custom-template-1',
    enabled: false
  }
};

/**
 * 获取分类元数据
 */
export function getCategoryMetadata(category: CertificateCategory): TemplateCategory | undefined {
  return CATEGORY_METADATA[category];
}

/**
 * 获取启用的分类元数据列表
 */
export function getEnabledCategoryMetadata(): TemplateCategory[] {
  return Object.values(CATEGORY_METADATA).filter(category => category.enabled);
}

/**
 * 获取所有分类元数据列表
 */
export function getAllCategoryMetadata(): TemplateCategory[] {
  return Object.values(CATEGORY_METADATA);
}

/**
 * 根据URL slug获取分类
 */
export function getCategoryBySlug(slug: string): CertificateCategory | undefined {
  const category = Object.values(CATEGORY_METADATA).find(cat => cat.urlSlug === slug);
  return category?.id;
}

/**
 * 生成分类的SEO URL
 */
export function generateCategoryUrl(category: CertificateCategory): string {
  const metadata = getCategoryMetadata(category);
  return metadata ? `/categories/${metadata.urlSlug}` : `/categories/${category}`;
}
