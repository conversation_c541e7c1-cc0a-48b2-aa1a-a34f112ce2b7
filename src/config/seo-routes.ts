import { CertificateCategory } from '@/types/certificate';
import { getCategoryMetadata } from './category-metadata';

/**
 * SEO路由配置
 * 基于Google SEO最佳实践设计的URL结构
 */

export interface SEORoute {
  path: string;
  category: CertificateCategory;
  priority: number; // 0.1 - 1.0 for sitemap
  changeFreq: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  lastMod?: string;
}

/**
 * 主要分类路由配置
 * 按SEO优先级排序
 */
export const CATEGORY_ROUTES: SEORoute[] = [
  // 高搜索量分类 - 最高优先级
  {
    path: '/gift-certificates',
    category: CertificateCategory.GIFT_CERTIFICATE,
    priority: 0.9,
    changeFreq: 'weekly'
  },
  {
    path: '/award-certificates',
    category: CertificateCategory.AWARD_CERTIFICATE,
    priority: 0.9,
    changeFreq: 'weekly'
  },
  {
    path: '/graduation-certificates',
    category: CertificateCategory.GRADUATION_CERTIFICATE,
    priority: 0.8,
    changeFreq: 'monthly' // 季节性需求
  },
  {
    path: '/sports-certificates',
    category: CertificateCategory.SPORTS_CERTIFICATE,
    priority: 0.8,
    changeFreq: 'weekly'
  },
  {
    path: '/basketball-certificates',
    category: CertificateCategory.BASKETBALL_CERTIFICATE,
    priority: 0.7,
    changeFreq: 'weekly'
  },
  {
    path: '/football-certificates',
    category: CertificateCategory.FOOTBALL_CERTIFICATE,
    priority: 0.7,
    changeFreq: 'weekly'
  },
  
  // 原有分类
  {
    path: '/completion-certificates',
    category: CertificateCategory.COMPLETION,
    priority: 0.8,
    changeFreq: 'weekly'
  },
  {
    path: '/achievement-certificates',
    category: CertificateCategory.ACHIEVEMENT,
    priority: 0.8,
    changeFreq: 'weekly'
  },
  
  // 其他新分类
  {
    path: '/training-certificates',
    category: CertificateCategory.TRAINING_CERTIFICATE,
    priority: 0.7,
    changeFreq: 'weekly'
  },
  {
    path: '/employee-certificates',
    category: CertificateCategory.EMPLOYEE_CERTIFICATE,
    priority: 0.7,
    changeFreq: 'weekly'
  },
  {
    path: '/volunteer-certificates',
    category: CertificateCategory.VOLUNTEER_CERTIFICATE,
    priority: 0.6,
    changeFreq: 'monthly'
  },
  {
    path: '/recognition-certificates',
    category: CertificateCategory.RECOGNITION_CERTIFICATE,
    priority: 0.6,
    changeFreq: 'weekly'
  },
  {
    path: '/funny-certificates',
    category: CertificateCategory.FUNNY_CERTIFICATE,
    priority: 0.5,
    changeFreq: 'monthly'
  },
  {
    path: '/birth-certificates',
    category: CertificateCategory.BIRTH_CERTIFICATE,
    priority: 0.4,
    changeFreq: 'yearly' // 法律考虑，较低优先级
  },
  
  // 原有分类 - 较低优先级
  {
    path: '/participation-certificates',
    category: CertificateCategory.PARTICIPATION,
    priority: 0.6,
    changeFreq: 'weekly'
  },
  {
    path: '/excellence-certificates',
    category: CertificateCategory.EXCELLENCE,
    priority: 0.6,
    changeFreq: 'weekly'
  }
];

/**
 * 生成分类页面的完整URL
 */
export function generateCategoryPageUrl(category: CertificateCategory): string {
  const route = CATEGORY_ROUTES.find(r => r.category === category);
  return route?.path || `/categories/${category}`;
}

/**
 * 生成模板页面的URL
 */
export function generateTemplatePageUrl(category: CertificateCategory, templateId: string): string {
  const categoryPath = generateCategoryPageUrl(category);
  return `${categoryPath}/${templateId}`;
}

/**
 * 获取sitemap数据
 */
export function getSitemapData(): Array<{
  url: string;
  lastModified?: Date;
  changeFrequency?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}> {
  return CATEGORY_ROUTES.map(route => ({
    url: route.path,
    lastModified: route.lastMod ? new Date(route.lastMod) : new Date(),
    changeFrequency: route.changeFreq,
    priority: route.priority
  }));
}

/**
 * 面包屑导航配置
 */
export interface BreadcrumbItem {
  name: string;
  url: string;
}

/**
 * 生成分类页面的面包屑导航
 */
export function generateCategoryBreadcrumbs(category: CertificateCategory): BreadcrumbItem[] {
  const metadata = getCategoryMetadata(category);
  const categoryUrl = generateCategoryPageUrl(category);
  
  return [
    { name: 'Home', url: '/' },
    { name: 'Certificate Templates', url: '/templates' },
    { name: metadata?.displayName || category, url: categoryUrl }
  ];
}

/**
 * 生成模板页面的面包屑导航
 */
export function generateTemplateBreadcrumbs(
  category: CertificateCategory, 
  templateName: string,
  templateId: string
): BreadcrumbItem[] {
  const categoryBreadcrumbs = generateCategoryBreadcrumbs(category);
  const templateUrl = generateTemplatePageUrl(category, templateId);
  
  return [
    ...categoryBreadcrumbs,
    { name: templateName, url: templateUrl }
  ];
}

/**
 * 关键词密度优化建议
 */
export const SEO_KEYWORD_GUIDELINES = {
  // 主要关键词密度建议 (1-3%)
  primaryKeywordDensity: { min: 0.01, max: 0.03 },
  
  // 次要关键词密度建议 (0.5-1.5%)
  secondaryKeywordDensity: { min: 0.005, max: 0.015 },
  
  // 标题长度建议
  titleLength: { min: 30, max: 60 },
  
  // 描述长度建议
  descriptionLength: { min: 120, max: 160 },
  
  // H1标签建议
  h1Guidelines: {
    maxLength: 70,
    shouldIncludePrimaryKeyword: true,
    shouldBeUnique: true
  },
  
  // 内部链接建议
  internalLinkGuidelines: {
    minLinksPerPage: 3,
    maxLinksPerPage: 100,
    shouldUseDescriptiveAnchorText: true
  }
};

/**
 * 结构化数据模板
 */
export const STRUCTURED_DATA_TEMPLATES = {
  categoryPage: (category: CertificateCategory) => {
    const metadata = getCategoryMetadata(category);
    return {
      '@context': 'https://schema.org',
      '@type': 'CollectionPage',
      name: metadata?.displayName,
      description: metadata?.description,
      url: `https://certificatemaker.app${generateCategoryPageUrl(category)}`,
      mainEntity: {
        '@type': 'ItemList',
        name: `${metadata?.displayName} Templates`,
        description: metadata?.description,
        numberOfItems: metadata?.templateCount || 0
      }
    };
  },
  
  templatePage: (templateName: string, templateDescription: string, category: CertificateCategory) => ({
    '@context': 'https://schema.org',
    '@type': 'CreativeWork',
    name: templateName,
    description: templateDescription,
    category: getCategoryMetadata(category)?.displayName,
    creator: {
      '@type': 'Organization',
      name: 'Certificate Maker'
    },
    isAccessibleForFree: true,
    license: 'https://creativecommons.org/licenses/by/4.0/'
  })
};

/**
 * 获取高优先级路由（用于预渲染）
 */
export function getHighPriorityRoutes(): string[] {
  return CATEGORY_ROUTES
    .filter(route => route.priority >= 0.8)
    .map(route => route.path);
}

/**
 * 获取季节性路由（用于动态优先级调整）
 */
export function getSeasonalRoutes(): { path: string; season: string }[] {
  return [
    { path: '/graduation-certificates', season: 'spring-summer' },
    { path: '/gift-certificates', season: 'winter-holidays' },
    { path: '/sports-certificates', season: 'fall-spring' }
  ];
}
