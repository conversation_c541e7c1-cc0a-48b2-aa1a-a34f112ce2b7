# SEO优化的路由结构指南

## 建议的Next.js App Router结构

基于SEO最佳实践，建议创建以下页面结构：

### 1. 主要分类页面 (高搜索量优先)

```
src/app/
├── gift-certificates/
│   ├── page.tsx                    # 礼品证书分类页面
│   ├── [templateId]/
│   │   └── page.tsx               # 具体模板页面
│   └── layout.tsx                 # 分类布局
├── award-certificates/
│   ├── page.tsx                   # 奖励证书分类页面
│   ├── [templateId]/
│   │   └── page.tsx
│   └── layout.tsx
├── graduation-certificates/
│   ├── page.tsx                   # 毕业证书分类页面
│   ├── [templateId]/
│   │   └── page.tsx
│   └── layout.tsx
├── sports-certificates/
│   ├── page.tsx                   # 体育证书分类页面
│   ├── [templateId]/
│   │   └── page.tsx
│   └── layout.tsx
├── basketball-certificates/
│   ├── page.tsx                   # 篮球证书分类页面
│   ├── [templateId]/
│   │   └── page.tsx
│   └── layout.tsx
└── football-certificates/
    ├── page.tsx                   # 足球证书分类页面
    ├── [templateId]/
    │   └── page.tsx
    └── layout.tsx
```

### 2. 其他分类页面

```
src/app/
├── training-certificates/
├── employee-certificates/
├── volunteer-certificates/
├── recognition-certificates/
├── funny-certificates/
├── birth-certificates/
├── completion-certificates/      # 现有
├── achievement-certificates/     # 现有
├── participation-certificates/
└── excellence-certificates/
```

### 3. SEO元数据配置示例

每个分类页面的 `page.tsx` 应包含：

```typescript
import { Metadata } from 'next';
import { getCategoryMetadata } from '@/config/category-metadata';
import { CertificateCategory } from '@/types/certificate';

export async function generateMetadata(): Promise<Metadata> {
  const metadata = getCategoryMetadata(CertificateCategory.GIFT_CERTIFICATE);
  
  return {
    title: metadata?.metaTitle,
    description: metadata?.metaDescription,
    keywords: metadata?.seoKeywords,
    openGraph: {
      title: metadata?.metaTitle,
      description: metadata?.metaDescription,
      type: 'website',
      url: `https://certificatemaker.app/gift-certificates`,
    },
    alternates: {
      canonical: 'https://certificatemaker.app/gift-certificates',
    },
  };
}
```

### 4. 结构化数据实现

```typescript
import { STRUCTURED_DATA_TEMPLATES } from '@/config/seo-routes';

export default function GiftCertificatesPage() {
  const structuredData = STRUCTURED_DATA_TEMPLATES.categoryPage(
    CertificateCategory.GIFT_CERTIFICATE
  );

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      {/* 页面内容 */}
    </>
  );
}
```

### 5. 面包屑导航实现

```typescript
import { generateCategoryBreadcrumbs } from '@/config/seo-routes';

export default function CategoryPage({ params }: { params: { category: string } }) {
  const breadcrumbs = generateCategoryBreadcrumbs(CertificateCategory.GIFT_CERTIFICATE);
  
  return (
    <nav aria-label="Breadcrumb">
      <ol className="breadcrumb">
        {breadcrumbs.map((item, index) => (
          <li key={index}>
            <a href={item.url}>{item.name}</a>
          </li>
        ))}
      </ol>
    </nav>
  );
}
```

### 6. Sitemap生成

在 `src/app/sitemap.ts` 中：

```typescript
import { MetadataRoute } from 'next';
import { getSitemapData } from '@/config/seo-routes';

export default function sitemap(): MetadataRoute.Sitemap {
  const categoryRoutes = getSitemapData();
  
  return [
    {
      url: 'https://certificatemaker.app',
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1,
    },
    ...categoryRoutes.map(route => ({
      url: `https://certificatemaker.app${route.url}`,
      lastModified: route.lastModified,
      changeFrequency: route.changeFrequency,
      priority: route.priority,
    })),
  ];
}
```

### 7. 内部链接策略

- 在首页突出显示高搜索量分类
- 在每个分类页面交叉链接相关分类
- 在模板页面链接回分类页面和相关模板
- 使用描述性锚文本，包含目标关键词

### 8. URL结构最佳实践

✅ **推荐的URL结构：**
- `/gift-certificates` - 简洁、包含关键词
- `/award-certificates` - 描述性、SEO友好
- `/basketball-certificates` - 具体、易理解

❌ **避免的URL结构：**
- `/categories/gift` - 过于通用
- `/cert/gift-cert` - 缩写不利于SEO
- `/templates/category/gift` - 过深的层级

### 9. 移动端优化

- 确保所有页面响应式设计
- 优化移动端加载速度
- 使用适当的视口设置
- 确保触摸友好的导航

### 10. 性能优化

- 使用Next.js的静态生成 (SSG) 对于高优先级页面
- 实现图片懒加载
- 优化字体加载
- 使用适当的缓存策略

## 实施优先级

1. **第一阶段**：实现高搜索量分类页面
   - gift-certificates
   - award-certificates
   - graduation-certificates

2. **第二阶段**：实现体育相关分类
   - sports-certificates
   - basketball-certificates
   - football-certificates

3. **第三阶段**：实现其他新分类
   - training-certificates
   - employee-certificates
   - 等等

4. **第四阶段**：优化现有分类页面
   - 更新completion-certificates
   - 更新achievement-certificates

## 监控和分析

- 使用Google Search Console监控搜索表现
- 设置Google Analytics跟踪用户行为
- 监控页面加载速度
- 定期检查内部链接健康状况
- 跟踪关键词排名变化
