/**
 * 证书分类系统使用示例
 * 展示如何在实际应用中使用新的分类系统
 */

import { CertificateCategory } from '@/types/certificate';
import { getCategoryMetadata, getAllCategoryMetadata } from '@/config/category-metadata';
import { generateCategoryPageUrl, generateCategoryBreadcrumbs, STRUCTURED_DATA_TEMPLATES } from '@/config/seo-routes';
import { getTemplatesByCategory, getHighSEOValueTemplates } from '@/lib/certificate-templates';
import { getEnabledCategories } from '@/config/categories';

/**
 * 示例1: 生成分类页面的SEO元数据
 */
export function generateCategoryPageMetadata(category: CertificateCategory) {
  const metadata = getCategoryMetadata(category);
  
  if (!metadata) {
    throw new Error(`Metadata not found for category: ${category}`);
  }

  return {
    title: metadata.metaTitle,
    description: metadata.metaDescription,
    keywords: metadata.seoKeywords.join(', '),
    openGraph: {
      title: metadata.metaTitle,
      description: metadata.metaDescription,
      type: 'website' as const,
      url: `https://certificatemaker.app${generateCategoryPageUrl(category)}`,
    },
    twitter: {
      card: 'summary_large_image' as const,
      title: metadata.metaTitle,
      description: metadata.metaDescription,
    },
    alternates: {
      canonical: `https://certificatemaker.app${generateCategoryPageUrl(category)}`,
    },
  };
}

/**
 * 示例2: 生成分类页面的结构化数据
 */
export function generateCategoryStructuredData(category: CertificateCategory) {
  const structuredData = STRUCTURED_DATA_TEMPLATES.categoryPage(category);
  
  return {
    __html: JSON.stringify(structuredData, null, 2)
  };
}

/**
 * 示例3: 生成导航菜单数据
 */
export function generateNavigationMenu() {
  const enabledCategories = getEnabledCategories();
  const allMetadata = getAllCategoryMetadata();
  
  return enabledCategories.map(category => {
    const metadata = getCategoryMetadata(category);
    return {
      name: metadata?.displayName || category,
      url: generateCategoryPageUrl(category),
      description: metadata?.description,
      templateCount: getTemplatesByCategory(category).length
    };
  });
}

/**
 * 示例4: 生成首页的高优先级分类展示
 */
export function generateHomepageCategories() {
  const highSEOCategories = [
    CertificateCategory.GIFT_CERTIFICATE,
    CertificateCategory.AWARD_CERTIFICATE,
    CertificateCategory.GRADUATION_CERTIFICATE,
    CertificateCategory.SPORTS_CERTIFICATE,
    CertificateCategory.BASKETBALL_CERTIFICATE,
    CertificateCategory.FOOTBALL_CERTIFICATE
  ];

  return highSEOCategories.map(category => {
    const metadata = getCategoryMetadata(category);
    const templates = getTemplatesByCategory(category);
    
    return {
      category,
      displayName: metadata?.displayName,
      description: metadata?.description,
      url: generateCategoryPageUrl(category),
      templateCount: templates.length,
      previewImage: templates[0]?.preview || '/images/category-placeholder.png',
      seoKeywords: metadata?.seoKeywords.slice(0, 3), // 显示前3个关键词
      isComingSoon: templates.length === 0
    };
  });
}

/**
 * 示例5: 生成面包屑导航组件数据
 */
export function generateBreadcrumbData(category: CertificateCategory, templateName?: string, templateId?: string) {
  const breadcrumbs = generateCategoryBreadcrumbs(category);
  
  if (templateName && templateId) {
    breadcrumbs.push({
      name: templateName,
      url: `${generateCategoryPageUrl(category)}/${templateId}`
    });
  }
  
  return breadcrumbs.map((item, index) => ({
    ...item,
    isLast: index === breadcrumbs.length - 1,
    position: index + 1
  }));
}

/**
 * 示例6: 生成相关分类推荐
 */
export function generateRelatedCategories(currentCategory: CertificateCategory, limit = 4) {
  const allCategories = getAllCategoryMetadata();
  const currentMetadata = getCategoryMetadata(currentCategory);
  
  if (!currentMetadata) return [];
  
  // 基于关键词相似性推荐相关分类
  const relatedCategories = allCategories
    .filter(cat => cat.id !== currentCategory && cat.enabled)
    .map(cat => {
      const commonKeywords = cat.seoKeywords.filter(keyword => 
        currentMetadata.seoKeywords.some(currentKeyword => 
          keyword.toLowerCase().includes(currentKeyword.toLowerCase()) ||
          currentKeyword.toLowerCase().includes(keyword.toLowerCase())
        )
      );
      
      return {
        category: cat,
        relevanceScore: commonKeywords.length,
        url: generateCategoryPageUrl(cat.id),
        templateCount: getTemplatesByCategory(cat.id).length
      };
    })
    .filter(item => item.relevanceScore > 0)
    .sort((a, b) => b.relevanceScore - a.relevanceScore)
    .slice(0, limit);
  
  return relatedCategories;
}

/**
 * 示例7: 生成搜索建议数据
 */
export function generateSearchSuggestions(query: string) {
  const allMetadata = getAllCategoryMetadata();
  const suggestions: Array<{
    type: 'category' | 'keyword';
    text: string;
    url: string;
    category?: CertificateCategory;
  }> = [];
  
  const queryLower = query.toLowerCase();
  
  // 搜索分类名称
  allMetadata.forEach(metadata => {
    if (metadata.displayName.toLowerCase().includes(queryLower)) {
      suggestions.push({
        type: 'category',
        text: metadata.displayName,
        url: generateCategoryPageUrl(metadata.id),
        category: metadata.id
      });
    }
    
    // 搜索关键词
    metadata.seoKeywords.forEach(keyword => {
      if (keyword.toLowerCase().includes(queryLower)) {
        suggestions.push({
          type: 'keyword',
          text: keyword,
          url: generateCategoryPageUrl(metadata.id),
          category: metadata.id
        });
      }
    });
  });
  
  // 去重并限制数量
  const uniqueSuggestions = suggestions
    .filter((item, index, self) => 
      index === self.findIndex(t => t.text === item.text)
    )
    .slice(0, 10);
  
  return uniqueSuggestions;
}

/**
 * 示例8: 生成sitemap条目
 */
export function generateSitemapEntries() {
  const allMetadata = getAllCategoryMetadata();
  
  return allMetadata
    .filter(metadata => metadata.enabled)
    .map(metadata => ({
      url: `https://certificatemaker.app${generateCategoryPageUrl(metadata.id)}`,
      lastModified: new Date().toISOString(),
      changeFrequency: 'weekly' as const,
      priority: getPriorityByCategory(metadata.id)
    }));
}

/**
 * 辅助函数：根据分类获取SEO优先级
 */
function getPriorityByCategory(category: CertificateCategory): number {
  const highPriorityCategories = [
    CertificateCategory.GIFT_CERTIFICATE,
    CertificateCategory.AWARD_CERTIFICATE,
    CertificateCategory.GRADUATION_CERTIFICATE
  ];
  
  const mediumPriorityCategories = [
    CertificateCategory.SPORTS_CERTIFICATE,
    CertificateCategory.BASKETBALL_CERTIFICATE,
    CertificateCategory.FOOTBALL_CERTIFICATE,
    CertificateCategory.COMPLETION,
    CertificateCategory.ACHIEVEMENT
  ];
  
  if (highPriorityCategories.includes(category)) return 0.9;
  if (mediumPriorityCategories.includes(category)) return 0.8;
  return 0.6;
}

/**
 * 示例9: 生成分析数据
 */
export function generateAnalyticsData() {
  const allMetadata = getAllCategoryMetadata();
  
  return {
    totalCategories: allMetadata.length,
    enabledCategories: allMetadata.filter(m => m.enabled).length,
    highSEOCategories: allMetadata.filter(m => 
      [
        CertificateCategory.GIFT_CERTIFICATE,
        CertificateCategory.AWARD_CERTIFICATE,
        CertificateCategory.GRADUATION_CERTIFICATE,
        CertificateCategory.SPORTS_CERTIFICATE
      ].includes(m.id)
    ).length,
    categoriesWithTemplates: allMetadata.filter(m => 
      getTemplatesByCategory(m.id).length > 0
    ).length,
    totalKeywords: allMetadata.reduce((sum, m) => sum + m.seoKeywords.length, 0),
    averageKeywordsPerCategory: Math.round(
      allMetadata.reduce((sum, m) => sum + m.seoKeywords.length, 0) / allMetadata.length
    )
  };
}

/**
 * 示例10: 生成A/B测试配置
 */
export function generateABTestConfig() {
  return {
    homepageCategoryOrder: {
      variant_a: [
        CertificateCategory.GIFT_CERTIFICATE,
        CertificateCategory.AWARD_CERTIFICATE,
        CertificateCategory.COMPLETION,
        CertificateCategory.ACHIEVEMENT
      ],
      variant_b: [
        CertificateCategory.AWARD_CERTIFICATE,
        CertificateCategory.GIFT_CERTIFICATE,
        CertificateCategory.GRADUATION_CERTIFICATE,
        CertificateCategory.SPORTS_CERTIFICATE
      ]
    },
    categoryDescriptionLength: {
      variant_a: 'short', // 使用简短描述
      variant_b: 'full'   // 使用完整描述
    }
  };
}
