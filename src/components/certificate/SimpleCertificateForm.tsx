'use client';

import React from 'react';
import { CertificateData, CertificateTemplate, CertificateCategory } from '@/types/certificate';

interface SimpleCertificateFormProps {
  template: CertificateTemplate;
  formData: CertificateData;
  onFormDataChange: (data: CertificateData) => void;
}

export default function SimpleCertificateForm({
  template,
  formData,
  onFormDataChange,
}: SimpleCertificateFormProps) {
  const handleInputChange = (field: keyof CertificateData, value: string) => {
    onFormDataChange({
      ...formData,
      [field]: value,
    });
  };

  // Short placeholder text in English
  const getDetailsPlaceholder = (category: CertificateCategory): string => {
    const placeholders: Partial<Record<CertificateCategory, string>> = {
      [CertificateCategory.ACHIEVEMENT]: "Enter achievement details",
      [CertificateCategory.COMPLETION]: "Enter course or program completed",
      [CertificateCategory.PARTICIPATION]: "Enter event or activity participated",
      [CertificateCategory.EXCELLENCE]: "Enter area of excellence",
      [CertificateCategory.CUSTOM]: "Enter certificate details",
      [CertificateCategory.GIFT_CERTIFICATE]: "Enter gift certificate value or description",
      [CertificateCategory.AWARD_CERTIFICATE]: "Enter award details and reason",
      [CertificateCategory.GRADUATION_CERTIFICATE]: "Enter degree or program completed",
      [CertificateCategory.SPORTS_CERTIFICATE]: "Enter sport and achievement details",
      [CertificateCategory.BASKETBALL_CERTIFICATE]: "Enter basketball achievement or position",
      [CertificateCategory.FOOTBALL_CERTIFICATE]: "Enter football achievement or position",
      [CertificateCategory.TRAINING_CERTIFICATE]: "Enter training program completed",
      [CertificateCategory.EMPLOYEE_CERTIFICATE]: "Enter employee recognition details",
      [CertificateCategory.VOLUNTEER_CERTIFICATE]: "Enter volunteer service details",
      [CertificateCategory.RECOGNITION_CERTIFICATE]: "Enter recognition details",
      [CertificateCategory.FUNNY_CERTIFICATE]: "Enter humorous achievement or reason",
      [CertificateCategory.BIRTH_CERTIFICATE]: "Enter birth details (commemorative only)"
    };

    return placeholders[category] || placeholders[CertificateCategory.CUSTOM] || "Enter certificate details";
  };

  const detailsPlaceholder = getDetailsPlaceholder(template.category);

  return (
    <div className="space-y-6">
      {/* Name Field */}
      <div>
        <label htmlFor="name" className="block text-sm font-semibold text-gray-800 mb-3">
          Recipient Name *
        </label>
        <input
          type="text"
          id="name"
          placeholder="Enter recipient name"
          value={formData.recipientName}
          onChange={(e) => handleInputChange('recipientName', e.target.value)}
          maxLength={template.constraints.nameMaxLength}
          className="w-full px-4 py-3 h-12 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base transition-all duration-200 hover:border-gray-300 min-h-[44px] touch-manipulation"
          required
        />
        <p className="text-xs text-gray-500 mt-1">
          Maximum {template.constraints.nameMaxLength} characters
        </p>
      </div>

      {/* Details Field */}
      <div>
        <label htmlFor="subtitle" className="block text-sm font-semibold text-gray-800 mb-3">
          Certificate Details *
        </label>
        <textarea
          id="subtitle"
          placeholder={detailsPlaceholder}
          value={formData.details}
          onChange={(e) => handleInputChange('details', e.target.value)}
          maxLength={template.constraints.detailsMaxLength}
          className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-base transition-all duration-200 hover:border-gray-300 min-h-[88px] touch-manipulation"
          rows={3}
          required
        />
        <p className="text-xs text-gray-500 mt-1">
          Maximum {template.constraints.detailsMaxLength} characters
        </p>
      </div>

      {/* Date and Signature in a row for better layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Date Field */}
        <div>
          <label htmlFor="date" className="block text-sm font-semibold text-gray-800 mb-3">
            Date *
          </label>
          <input
            type="text"
            id="date"
            placeholder="Enter date"
            value={formData.date}
            onChange={(e) => handleInputChange('date', e.target.value)}
            maxLength={template.constraints.dateMaxLength}
            className="w-full px-4 py-3 h-12 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base transition-all duration-200 hover:border-gray-300 min-h-[44px] touch-manipulation"
            required
          />
          <p className="text-xs text-gray-500 mt-1">
            Maximum {template.constraints.dateMaxLength} characters
          </p>
        </div>

        {/* Signature Field */}
        <div>
          <label htmlFor="signature" className="block text-sm font-semibold text-gray-800 mb-3">
            Signature *
          </label>
          <input
            type="text"
            id="signature"
            placeholder="Enter signature"
            value={formData.signature}
            onChange={(e) => handleInputChange('signature', e.target.value)}
            maxLength={template.constraints.signatureMaxLength}
            className="w-full px-4 py-3 h-12 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base transition-all duration-200 hover:border-gray-300 min-h-[44px] touch-manipulation"
            required
          />
          <p className="text-xs text-gray-500 mt-1">
            Maximum {template.constraints.signatureMaxLength} characters
          </p>
        </div>
      </div>

    </div>
  );
}
